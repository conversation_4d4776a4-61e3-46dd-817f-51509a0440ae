package com.chic.dea.infrastructure.general.constants;

/**
 * @Auther: wallace
 * @Date: 2022/11/15 17:57
 * @Description: redis key 规范：用途+类型
 */
public class RedisKeyConstants {

  public static final String REDIS_KEY_SEPARATOR = "_";

  public static final int ERR_NEXTVAL_FAILED = -1;

  public static final String NEXTVAL_DEFAUTL = "0";

  /* REDIS缓存前缀 */
  public static final String REDIS_PREFIX = "data-extract-audit:";

  /* 主键KEY */
  public static final String REDIS_SERIALNUMBER_LOCKPREFIX_PREFIX = REDIS_PREFIX + "lockMaxNo";

  /* 数据源缓存KEY */
  public static final String DATASOURCE_CACHE_PREFIX = REDIS_PREFIX + "datasource" + REDIS_KEY_SEPARATOR;


}
